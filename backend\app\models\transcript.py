from datetime import datetime
from app import db


class Transcript(db.Model):
    """Transcript model for storing video transcriptions."""
    
    __tablename__ = 'transcripts'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    video_id = db.<PERSON>umn(db.<PERSON><PERSON><PERSON>, db.<PERSON>('videos.id'), nullable=False)
    
    # Transcript content
    full_text = db.Column(db.Text, nullable=False)
    segments_data = db.Column(db.JSON)  # Timestamped segments from Whisper
    
    # Processing information
    language = db.Column(db.String(10))  # Language code (e.g., 'en', 'es')
    confidence = db.Column(db.Float)  # Overall confidence score
    processing_time = db.Column(db.Float)  # Time taken to transcribe in seconds
    
    # Whisper-specific data
    whisper_model = db.Column(db.String(50))  # Model used (e.g., 'base', 'large')
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def __init__(self, video_id, full_text, **kwargs):
        self.video_id = video_id
        self.full_text = full_text
        
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def get_text_at_time(self, start_time, end_time):
        """Get transcript text for a specific time range."""
        if not self.segments_data:
            return ""
        
        text_segments = []
        for segment in self.segments_data:
            segment_start = segment.get('start', 0)
            segment_end = segment.get('end', 0)
            
            # Check if segment overlaps with requested time range
            if segment_start < end_time and segment_end > start_time:
                text_segments.append(segment.get('text', ''))
        
        return ' '.join(text_segments).strip()
    
    def get_segments_in_range(self, start_time, end_time):
        """Get transcript segments within a time range."""
        if not self.segments_data:
            return []
        
        segments = []
        for segment in self.segments_data:
            segment_start = segment.get('start', 0)
            segment_end = segment.get('end', 0)
            
            if segment_start < end_time and segment_end > start_time:
                segments.append(segment)
        
        return segments
    
    def search_text(self, query, case_sensitive=False):
        """Search for text in transcript and return matching segments with timestamps."""
        if not self.segments_data or not query:
            return []
        
        if not case_sensitive:
            query = query.lower()
        
        matches = []
        for segment in self.segments_data:
            text = segment.get('text', '')
            if not case_sensitive:
                text = text.lower()
            
            if query in text:
                matches.append({
                    'start': segment.get('start'),
                    'end': segment.get('end'),
                    'text': segment.get('text'),
                    'confidence': segment.get('confidence')
                })
        
        return matches
    
    def get_word_count(self):
        """Get total word count of transcript."""
        return len(self.full_text.split()) if self.full_text else 0
    
    def get_duration(self):
        """Get total duration covered by transcript."""
        if not self.segments_data:
            return 0
        
        if self.segments_data:
            last_segment = max(self.segments_data, key=lambda x: x.get('end', 0))
            return last_segment.get('end', 0)
        
        return 0
    
    def to_dict(self, include_segments=True):
        """Convert transcript to dictionary."""
        data = {
            'id': self.id,
            'video_id': self.video_id,
            'full_text': self.full_text,
            'language': self.language,
            'confidence': self.confidence,
            'processing_time': self.processing_time,
            'whisper_model': self.whisper_model,
            'word_count': self.get_word_count(),
            'duration': self.get_duration(),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
        
        if include_segments:
            data['segments'] = self.segments_data
        
        return data
    
    def __repr__(self):
        return f'<Transcript for Video {self.video_id}>'
