# UniDynamics

**Transform lengthy YouTube videos into concise, engaging summaries**

UniDynamics empowers users to create intelligent video summaries by combining the best parts of multiple Creative Commons licensed YouTube videos into a single, coherent piece.

## 🎯 Project Status

**✅ PHASE 1 (MVP) - IMPLEMENTED**
- Complete backend API with Flask + Celery
- React frontend with Material-UI
- User authentication and project management
- YouTube video processing pipeline
- AI-powered segment analysis with Ollama
- Video transcription with Whisper
- Basic video editing and rendering
- Docker containerization

**🔄 PHASE 2 - IN PROGRESS**
- Multi-video compilation features
- Advanced timeline interface
- Enhanced AI capabilities

## 🚀 Features

### ✅ Implemented (Phase 1)
- **User Management**: Registration, login, profile management
- **Project System**: Create and manage video summarization projects
- **YouTube Integration**: URL validation, metadata extraction, license checking
- **AI Analysis**: Ollama-powered transcript analysis and segment suggestions
- **Video Processing**: FFmpeg-based video downloading, trimming, and concatenation
- **Transcription**: Whisper-based automatic speech recognition
- **Cloud Storage**: AWS S3 and Google Cloud Storage support
- **Task Queue**: Celery-based asynchronous processing
- **API**: Comprehensive REST API with JWT authentication

### 🔄 In Development (Phase 2)
- Multi-video compilation
- Advanced timeline editor
- Drag-and-drop interface
- Transitions and effects
- Intro/outro support

### 📋 Planned (Phase 3)
- Advanced AI summarization
- Text overlays and effects
- Background music integration
- Direct YouTube re-upload
- AI-generated metadata

## 🛠 Technology Stack

- **Frontend**: React 18, Material-UI, Video.js, Axios
- **Backend**: Python Flask, SQLAlchemy, Celery, JWT
- **AI/ML**: Ollama (local LLMs), OpenAI Whisper
- **Video**: FFmpeg, MoviePy, yt-dlp
- **Database**: PostgreSQL
- **Cache/Queue**: Redis
- **Storage**: AWS S3 / Google Cloud Storage
- **Infrastructure**: Docker, Docker Compose

## 🚀 Quick Start

### Option 1: Docker (Recommended)
```bash
# Clone repository
git clone <repository-url>
cd unidynamics

# Run setup script
chmod +x scripts/setup.sh
./scripts/setup.sh

# Choose option 1 for full Docker setup
# Access at http://localhost:3000
```

### Option 2: Manual Setup
```bash
# 1. Clone and setup
git clone <repository-url>
cd unidynamics

# 2. Backend setup
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
cp .env.example .env
# Edit .env with your configuration

# 3. Frontend setup
cd ../frontend
npm install
cp .env.example .env
# Edit .env with your configuration

# 4. Start services (separate terminals)
# Terminal 1: PostgreSQL and Redis
docker-compose up -d postgres redis

# Terminal 2: Ollama
docker-compose up -d ollama
docker-compose exec ollama ollama pull llama2

# Terminal 3: Backend
cd backend
python run.py

# Terminal 4: Celery Worker
cd backend
celery -A app.celery worker --loglevel=info

# Terminal 5: Frontend
cd frontend
npm start
```

## 📁 Project Structure

```
unidynamics/
├── backend/                 # Python Flask API
│   ├── app/
│   │   ├── models/         # Database models (User, Project, Video, etc.)
│   │   ├── routes/         # API endpoints (auth, projects, videos)
│   │   ├── services/       # Business logic (YouTube, AI, transcription)
│   │   ├── tasks/          # Celery tasks (video processing, AI analysis)
│   │   └── utils/          # Utility functions
│   ├── migrations/         # Database migrations
│   ├── config.py          # Configuration management
│   ├── run.py             # Application entry point
│   └── requirements.txt   # Python dependencies
├── frontend/               # React application
│   ├── src/
│   │   ├── components/     # Reusable React components
│   │   ├── pages/          # Page components (Dashboard, Projects, etc.)
│   │   ├── services/       # API clients and utilities
│   │   ├── contexts/       # React contexts (Auth, etc.)
│   │   └── utils/          # Frontend utilities
│   ├── public/            # Static assets
│   └── package.json       # Node.js dependencies
├── docker/                # Docker configurations
│   ├── docker-compose.yml # Multi-service setup
│   ├── Dockerfile.backend # Backend container
│   └── Dockerfile.frontend# Frontend container
├── scripts/               # Setup and deployment scripts
│   ├── setup.sh          # Linux/macOS setup
│   └── setup.ps1         # Windows PowerShell setup
├── docs/                  # Documentation
│   ├── API.md            # API documentation
│   ├── DEVELOPMENT.md    # Development guide
│   └── DEPLOYMENT.md     # Deployment guide
├── tests/                 # Test files
└── README.md             # This file
```

## 🧪 Testing

### Run Backend Tests
```bash
cd backend
python -m pytest tests/ -v
```

### Run Frontend Tests
```bash
cd frontend
npm test
```

### Run Integration Tests
```bash
# Start services
docker-compose up -d

# Run tests
python tests/test_basic_functionality.py
```

## 📚 Documentation

- **[API Documentation](docs/API.md)**: Complete REST API reference
- **[Development Guide](docs/DEVELOPMENT.md)**: Detailed development setup and workflows
- **[Deployment Guide](docs/DEPLOYMENT.md)**: Production deployment instructions

## 🔧 Configuration

### Backend Configuration (.env)
```bash
# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/unidynamics

# Redis
REDIS_URL=redis://localhost:6379/0

# YouTube API
YOUTUBE_API_KEY=your-youtube-api-key

# Ollama
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama2

# Cloud Storage (choose one)
AWS_ACCESS_KEY_ID=your-aws-key
AWS_SECRET_ACCESS_KEY=your-aws-secret
S3_BUCKET=your-bucket

# OR

GOOGLE_CLOUD_PROJECT=your-project
GCS_BUCKET=your-bucket
```

### Frontend Configuration (.env)
```bash
REACT_APP_API_BASE_URL=http://localhost:5000/api
REACT_APP_GOOGLE_CLIENT_ID=your-google-client-id
```

## 🚀 Deployment

### Development
```bash
# Full Docker setup
cd docker
docker-compose up

# Access application
# Frontend: http://localhost:3000
# Backend: http://localhost:5000
# Ollama: http://localhost:11434
```

### Production
See [docs/DEPLOYMENT.md](docs/DEPLOYMENT.md) for detailed production deployment instructions.

## 🤝 Contributing

1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Commit** your changes (`git commit -m 'Add amazing feature'`)
4. **Push** to the branch (`git push origin feature/amazing-feature`)
5. **Open** a Pull Request

### Development Guidelines
- Follow PEP 8 for Python code
- Use ESLint/Prettier for JavaScript code
- Write tests for new features
- Update documentation as needed
- Ensure all tests pass before submitting PR

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚖️ Legal Notice

UniDynamics is designed to work exclusively with Creative Commons licensed content. Users are responsible for ensuring they have the right to use and redistribute any content processed through the platform.

## 🆘 Support

- **Documentation**: Check the [docs/](docs/) directory
- **Issues**: Report bugs on [GitHub Issues](https://github.com/your-org/unidynamics/issues)
- **Discussions**: Join our [GitHub Discussions](https://github.com/your-org/unidynamics/discussions)

## 🙏 Acknowledgments

- **OpenAI Whisper** for speech recognition
- **Ollama** for local LLM inference
- **FFmpeg** for video processing
- **Material-UI** for React components
- **Flask** and **React** communities
