# UniDynamics

**Transform lengthy YouTube videos into concise, engaging summaries**

UniDynamics empowers users to create intelligent video summaries by combining the best parts of multiple Creative Commons licensed YouTube videos into a single, coherent piece.

## Features

### Phase 1 (MVP) - Single Video Summarization
- ✅ User authentication (email/password + Google OAuth)
- ✅ YouTube video URL input with license validation
- ✅ Automatic transcription using Whisper
- ✅ AI-powered segment suggestion using Ollama LLMs
- ✅ Interactive video editor for segment selection
- ✅ Video processing and rendering with FFmpeg
- ✅ Automatic attribution generation
- ✅ Download final summarized videos

### Phase 2 - Multi-Video Compilation
- 🔄 Multiple video input support
- 🔄 Multi-track timeline interface
- 🔄 Drag-and-drop clip arrangement
- 🔄 Basic transitions between clips
- 🔄 Intro/outro support

### Phase 3 - Advanced Features
- 🔄 Advanced AI summarization
- 🔄 Text overlays and effects
- 🔄 Background music integration
- 🔄 Direct YouTube re-upload
- 🔄 AI-generated metadata

## Technology Stack

- **Frontend**: React with Video.js, Timeline components
- **Backend**: Python Flask with Celery task queue
- **AI**: Ollama (local LLMs), OpenAI Whisper
- **Video Processing**: FFmpeg, MoviePy
- **Database**: PostgreSQL
- **Storage**: AWS S3 / Google Cloud Storage
- **Authentication**: JWT + OAuth 2.0

## Quick Start

### Prerequisites
- Python 3.9+
- Node.js 16+
- PostgreSQL
- Redis (for Celery)
- FFmpeg
- Ollama (for local LLMs)

### Installation

1. **Clone the repository**
```bash
git clone https://github.com/your-org/unidynamics.git
cd unidynamics
```

2. **Backend Setup**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

3. **Frontend Setup**
```bash
cd frontend
npm install
```

4. **Environment Configuration**
```bash
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env
# Edit the .env files with your configuration
```

5. **Database Setup**
```bash
cd backend
flask db upgrade
```

6. **Start Services**
```bash
# Terminal 1: Redis
redis-server

# Terminal 2: Celery Worker
cd backend
celery -A app.celery worker --loglevel=info

# Terminal 3: Backend API
cd backend
flask run

# Terminal 4: Frontend
cd frontend
npm start
```

## Project Structure

```
unidynamics/
├── backend/                 # Python Flask API
│   ├── app/
│   │   ├── models/         # Database models
│   │   ├── routes/         # API endpoints
│   │   ├── services/       # Business logic
│   │   └── tasks/          # Celery tasks
│   ├── migrations/         # Database migrations
│   └── requirements.txt
├── frontend/               # React application
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/          # Page components
│   │   ├── services/       # API clients
│   │   └── utils/          # Utilities
│   └── package.json
├── docker/                 # Docker configurations
├── docs/                   # Documentation
└── scripts/               # Deployment scripts
```

## Development Workflow

1. **Feature Development**: Create feature branches from `develop`
2. **Testing**: Run tests before committing
3. **Code Review**: Submit pull requests for review
4. **Deployment**: Merge to `main` triggers deployment

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Legal Notice

UniDynamics is designed to work exclusively with Creative Commons licensed content. Users are responsible for ensuring they have the right to use and redistribute any content processed through the platform.
