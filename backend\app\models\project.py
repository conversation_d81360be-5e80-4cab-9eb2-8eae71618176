from datetime import datetime
from enum import Enum
from app import db


class ProjectStatus(Enum):
    """Project status enumeration."""
    DRAFT = 'draft'
    PROCESSING = 'processing'
    COMPLETED = 'completed'
    FAILED = 'failed'


class Project(db.Model):
    """Project model for managing video summarization projects."""
    
    __tablename__ = 'projects'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Project details
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    
    # Status and progress
    status = db.Column(db.Enum(ProjectStatus), default=ProjectStatus.DRAFT, nullable=False)
    progress = db.Column(db.Float, default=0.0)  # 0.0 to 1.0
    
    # Output configuration
    output_filename = db.Column(db.String(255))
    output_url = db.Column(db.String(500))  # Cloud storage URL
    output_duration = db.Column(db.Float)  # Duration in seconds
    
    # Attribution and metadata
    attribution_text = db.Column(db.Text)
    generated_title = db.Column(db.String(200))
    generated_description = db.Column(db.Text)
    generated_tags = db.Column(db.JSON)
    
    # Processing settings
    max_duration = db.Column(db.Integer, default=600)  # Max output duration in seconds
    include_intro = db.Column(db.Boolean, default=False)
    include_outro = db.Column(db.Boolean, default=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    completed_at = db.Column(db.DateTime)
    
    # Relationships
    videos = db.relationship('Video', backref='project', lazy='dynamic', cascade='all, delete-orphan')
    segments = db.relationship('Segment', backref='project', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, user_id, title, **kwargs):
        self.user_id = user_id
        self.title = title
        
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def update_status(self, status, progress=None):
        """Update project status and progress."""
        self.status = status
        if progress is not None:
            self.progress = max(0.0, min(1.0, progress))
        
        if status == ProjectStatus.COMPLETED:
            self.completed_at = datetime.utcnow()
            self.progress = 1.0
        
        db.session.commit()
    
    def get_total_duration(self):
        """Get total duration of all selected segments."""
        total = 0.0
        for segment in self.segments:
            if segment.is_selected:
                total += segment.duration
        return total
    
    def get_source_videos_count(self):
        """Get count of source videos."""
        return self.videos.count()
    
    def generate_attribution(self):
        """Generate attribution text for all source videos."""
        attributions = []
        for video in self.videos:
            if video.title and video.channel_name:
                attribution = f'"{video.title}" by {video.channel_name}'
                if video.license_type:
                    attribution += f' (Licensed under {video.license_type})'
                attributions.append(attribution)
        
        if attributions:
            self.attribution_text = "Source videos: " + "; ".join(attributions)
        else:
            self.attribution_text = "Source videos processed through UniDynamics"
        
        db.session.commit()
        return self.attribution_text
    
    def to_dict(self, include_videos=False, include_segments=False):
        """Convert project to dictionary."""
        data = {
            'id': self.id,
            'user_id': self.user_id,
            'title': self.title,
            'description': self.description,
            'status': self.status.value,
            'progress': self.progress,
            'output_filename': self.output_filename,
            'output_url': self.output_url,
            'output_duration': self.output_duration,
            'attribution_text': self.attribution_text,
            'generated_title': self.generated_title,
            'generated_description': self.generated_description,
            'generated_tags': self.generated_tags,
            'max_duration': self.max_duration,
            'include_intro': self.include_intro,
            'include_outro': self.include_outro,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'total_duration': self.get_total_duration(),
            'source_videos_count': self.get_source_videos_count()
        }
        
        if include_videos:
            data['videos'] = [video.to_dict() for video in self.videos]
        
        if include_segments:
            data['segments'] = [segment.to_dict() for segment in self.segments]
        
        return data
    
    def __repr__(self):
        return f'<Project {self.title}>'
