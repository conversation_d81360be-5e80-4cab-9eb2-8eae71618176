# UniDynamics Setup Script for Windows PowerShell
# This script sets up the development environment for UniDynamics

Write-Host "🚀 Setting up UniDynamics Development Environment" -ForegroundColor Green
Write-Host "==================================================" -ForegroundColor Green

# Check if Docker is installed
try {
    docker --version | Out-Null
    Write-Host "✅ Docker is installed" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not installed. Please install Docker Desktop first." -ForegroundColor Red
    exit 1
}

# Check if Docker Compose is available
try {
    docker-compose --version | Out-Null
    Write-Host "✅ Docker Compose is available" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Compose is not available. Please install Docker Desktop with Compose." -ForegroundColor Red
    exit 1
}

# Check if Node.js is installed
try {
    node --version | Out-Null
    Write-Host "✅ Node.js is installed" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Node.js is not installed. You'll need it for local frontend development." -ForegroundColor Yellow
    Write-Host "   You can still use Docker for everything." -ForegroundColor Yellow
}

# Check if Python is installed
try {
    python --version | Out-Null
    Write-Host "✅ Python is installed" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Python is not installed. You'll need it for local backend development." -ForegroundColor Yellow
    Write-Host "   You can still use Docker for everything." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "📁 Setting up project structure..." -ForegroundColor Cyan

# Create necessary directories
$directories = @(
    "backend\uploads",
    "backend\logs", 
    "frontend\build",
    "docs\api",
    "tests\backend",
    "tests\frontend"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
    }
}

Write-Host "✅ Project directories created" -ForegroundColor Green

Write-Host ""
Write-Host "🔧 Setting up environment files..." -ForegroundColor Cyan

# Backend environment file
if (!(Test-Path "backend\.env")) {
    Copy-Item "backend\.env.example" "backend\.env"
    Write-Host "✅ Backend .env file created from example" -ForegroundColor Green
    Write-Host "   Please edit backend\.env with your configuration" -ForegroundColor Yellow
} else {
    Write-Host "ℹ️  Backend .env file already exists" -ForegroundColor Blue
}

# Frontend environment file
if (!(Test-Path "frontend\.env")) {
    Copy-Item "frontend\.env.example" "frontend\.env"
    Write-Host "✅ Frontend .env file created from example" -ForegroundColor Green
    Write-Host "   Please edit frontend\.env with your configuration" -ForegroundColor Yellow
} else {
    Write-Host "ℹ️  Frontend .env file already exists" -ForegroundColor Blue
}

Write-Host ""
Write-Host "🐳 Setting up Docker environment..." -ForegroundColor Cyan

# Change to docker directory
Set-Location docker

# Build and start infrastructure services
docker-compose up -d postgres redis

Write-Host "⏳ Waiting for database to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Initialize database
docker-compose run --rm backend python run.py init-db

Write-Host "✅ Database initialized" -ForegroundColor Green

Write-Host ""
Write-Host "🤖 Setting up Ollama (AI Models)..." -ForegroundColor Cyan

# Start Ollama
docker-compose up -d ollama

Write-Host "⏳ Waiting for Ollama to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 15

# Pull required models
Write-Host "📥 Downloading AI models (this may take a while)..." -ForegroundColor Cyan
docker-compose exec ollama ollama pull llama2
docker-compose exec ollama ollama pull mistral

Write-Host "✅ AI models downloaded" -ForegroundColor Green

Write-Host ""
Write-Host "🎯 Setup Options:" -ForegroundColor Cyan
Write-Host ""
Write-Host "1. 🐳 Full Docker Development (Recommended for beginners)" -ForegroundColor White
Write-Host "   - Everything runs in Docker containers" -ForegroundColor Gray
Write-Host "   - Run: cd docker && docker-compose up" -ForegroundColor Gray
Write-Host "   - Access: http://localhost:3000" -ForegroundColor Gray
Write-Host ""
Write-Host "2. 🔧 Hybrid Development (Recommended for developers)" -ForegroundColor White
Write-Host "   - Database and services in Docker" -ForegroundColor Gray
Write-Host "   - Frontend and backend run locally" -ForegroundColor Gray
Write-Host "   - Better for debugging and development" -ForegroundColor Gray
Write-Host ""
Write-Host "3. 💻 Local Development" -ForegroundColor White
Write-Host "   - Everything runs locally" -ForegroundColor Gray
Write-Host "   - Requires all dependencies installed" -ForegroundColor Gray
Write-Host ""

$choice = Read-Host "Choose setup option (1/2/3)"

switch ($choice) {
    "1" {
        Write-Host "🐳 Starting full Docker environment..." -ForegroundColor Cyan
        docker-compose up -d
        Write-Host ""
        Write-Host "✅ UniDynamics is running!" -ForegroundColor Green
        Write-Host "   Frontend: http://localhost:3000" -ForegroundColor White
        Write-Host "   Backend API: http://localhost:5000" -ForegroundColor White
        Write-Host "   Stop with: docker-compose down" -ForegroundColor Yellow
    }
    "2" {
        Write-Host "🔧 Setting up hybrid development..." -ForegroundColor Cyan
        
        # Keep only infrastructure services running
        docker-compose up -d postgres redis ollama
        
        Write-Host ""
        Write-Host "📦 Installing frontend dependencies..." -ForegroundColor Cyan
        Set-Location ..\frontend
        npm install
        
        Write-Host ""
        Write-Host "🐍 Setting up backend virtual environment..." -ForegroundColor Cyan
        Set-Location ..\backend
        python -m venv venv
        .\venv\Scripts\Activate.ps1
        pip install -r requirements.txt
        
        Write-Host ""
        Write-Host "✅ Hybrid setup complete!" -ForegroundColor Green
        Write-Host ""
        Write-Host "To start development:" -ForegroundColor White
        Write-Host "1. Backend: cd backend && .\venv\Scripts\Activate.ps1 && python run.py" -ForegroundColor Gray
        Write-Host "2. Frontend: cd frontend && npm start" -ForegroundColor Gray
        Write-Host "3. Celery Worker: cd backend && .\venv\Scripts\Activate.ps1 && celery -A app.celery worker --loglevel=info" -ForegroundColor Gray
    }
    "3" {
        Write-Host "💻 Local development setup..." -ForegroundColor Cyan
        Write-Host "Please ensure you have:" -ForegroundColor White
        Write-Host "- PostgreSQL running on localhost:5432" -ForegroundColor Gray
        Write-Host "- Redis running on localhost:6379" -ForegroundColor Gray
        Write-Host "- Ollama running on localhost:11434" -ForegroundColor Gray
        Write-Host ""
        Write-Host "Then follow the hybrid setup instructions." -ForegroundColor Yellow
    }
    default {
        Write-Host "Invalid choice. Please run the script again." -ForegroundColor Red
        exit 1
    }
}

Write-Host ""
Write-Host "🎉 Setup complete!" -ForegroundColor Green
Write-Host ""
Write-Host "📚 Next steps:" -ForegroundColor Cyan
Write-Host "1. Read the README.md for detailed instructions" -ForegroundColor White
Write-Host "2. Check the documentation in docs/" -ForegroundColor White
Write-Host "3. Create your first project at http://localhost:3000" -ForegroundColor White
Write-Host ""
Write-Host "🆘 Need help?" -ForegroundColor Cyan
Write-Host "- Check logs: docker-compose logs [service-name]" -ForegroundColor White
Write-Host "- Restart services: docker-compose restart" -ForegroundColor White
Write-Host "- Reset database: docker-compose run --rm backend python run.py reset-db" -ForegroundColor White
Write-Host ""
Write-Host "Happy coding! 🚀" -ForegroundColor Green
