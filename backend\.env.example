# Flask Configuration
FLASK_APP=app
FLASK_ENV=development
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/unidynamics

# Redis (for Celery)
REDIS_URL=redis://localhost:6379/0

# YouTube API
YOUTUBE_API_KEY=your-youtube-api-key-here

# OpenAI (optional, for Whisper API)
OPENAI_API_KEY=your-openai-api-key-here

# Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama2  # or mistral, codellama, etc.

# Cloud Storage (choose one)
# AWS S3
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
S3_BUCKET=unidynamics-storage

# Google Cloud Storage
GOOGLE_CLOUD_PROJECT=your-project-id
GCS_BUCKET=unidynamics-storage
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account.json

# File Upload Limits
MAX_CONTENT_LENGTH=**********  # 1GB in bytes
UPLOAD_FOLDER=uploads

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Security
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Rate Limiting
RATELIMIT_STORAGE_URL=redis://localhost:6379/1

# Monitoring
SENTRY_DSN=your-sentry-dsn-here

# Development
DEBUG=True
