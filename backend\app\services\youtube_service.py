import os
import yt_dlp
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from flask import current_app


class YouTubeService:
    """Service for YouTube API operations and video downloading."""
    
    def __init__(self):
        self.api_key = current_app.config.get('YOUTUBE_API_KEY')
        self.youtube_api = None
        
        if self.api_key:
            self.youtube_api = build('youtube', 'v3', developer<PERSON>ey=self.api_key)
    
    def get_video_metadata(self, video_id):
        """Get video metadata from YouTube API."""
        if not self.youtube_api:
            raise Exception("YouTube API key not configured")
        
        try:
            # Get video details
            video_response = self.youtube_api.videos().list(
                part='snippet,contentDetails,statistics,status',
                id=video_id
            ).execute()
            
            if not video_response['items']:
                raise Exception("Video not found")
            
            video_data = video_response['items'][0]
            snippet = video_data['snippet']
            content_details = video_data['contentDetails']
            statistics = video_data.get('statistics', {})
            status = video_data.get('status', {})
            
            # Parse duration (ISO 8601 format)
            duration = self._parse_duration(content_details['duration'])
            
            # Get channel details
            channel_response = self.youtube_api.channels().list(
                part='snippet',
                id=snippet['channelId']
            ).execute()
            
            channel_name = snippet['channelTitle']
            if channel_response['items']:
                channel_name = channel_response['items'][0]['snippet']['title']
            
            metadata = {
                'title': snippet['title'],
                'description': snippet['description'],
                'channel_name': channel_name,
                'channel_id': snippet['channelId'],
                'duration': duration,
                'view_count': int(statistics.get('viewCount', 0)),
                'like_count': int(statistics.get('likeCount', 0)),
                'published_at': snippet['publishedAt'],
                'license': status.get('license', 'youtube'),
                'privacy_status': status.get('privacyStatus', 'public'),
                'thumbnail_url': snippet['thumbnails']['high']['url']
            }
            
            return metadata
            
        except HttpError as e:
            current_app.logger.error(f"YouTube API error: {e}")
            raise Exception(f"YouTube API error: {e}")
        except Exception as e:
            current_app.logger.error(f"Error getting video metadata: {e}")
            raise
    
    def _parse_duration(self, duration_str):
        """Parse ISO 8601 duration to seconds."""
        import re
        
        # Remove 'PT' prefix
        duration_str = duration_str[2:]
        
        # Extract hours, minutes, seconds
        hours = 0
        minutes = 0
        seconds = 0
        
        # Hours
        hours_match = re.search(r'(\d+)H', duration_str)
        if hours_match:
            hours = int(hours_match.group(1))
        
        # Minutes
        minutes_match = re.search(r'(\d+)M', duration_str)
        if minutes_match:
            minutes = int(minutes_match.group(1))
        
        # Seconds
        seconds_match = re.search(r'(\d+)S', duration_str)
        if seconds_match:
            seconds = int(seconds_match.group(1))
        
        return hours * 3600 + minutes * 60 + seconds
    
    def is_video_republishable(self, metadata):
        """Check if video can be republished based on license."""
        license_type = metadata.get('license', '').lower()
        
        # Check for Creative Commons license
        if license_type == 'creativecommon':
            return True, 'creative_commons'
        
        # For now, we'll be conservative and only allow CC licensed videos
        return False, 'standard_youtube'
    
    def download_video(self, video_url, output_path):
        """Download video using yt-dlp."""
        try:
            ydl_opts = {
                'format': 'best[height<=720]',  # Limit to 720p for processing efficiency
                'outtmpl': os.path.join(output_path, '%(id)s.%(ext)s'),
                'writeinfojson': True,
                'writesubtitles': False,
                'writeautomaticsub': False,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                # Extract info first
                info = ydl.extract_info(video_url, download=False)
                
                # Check if video is available
                if info.get('availability') != 'public':
                    raise Exception("Video is not publicly available")
                
                # Download the video
                ydl.download([video_url])
                
                # Return file information
                video_id = info['id']
                ext = info['ext']
                filename = f"{video_id}.{ext}"
                filepath = os.path.join(output_path, filename)
                
                if not os.path.exists(filepath):
                    raise Exception("Downloaded file not found")
                
                file_size = os.path.getsize(filepath)
                
                return {
                    'filepath': filepath,
                    'filename': filename,
                    'file_size': file_size,
                    'format': ext,
                    'resolution': f"{info.get('width', 0)}x{info.get('height', 0)}"
                }
                
        except Exception as e:
            current_app.logger.error(f"Error downloading video: {e}")
            raise
    
    def get_video_info(self, video_url):
        """Get video info without downloading."""
        try:
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(video_url, download=False)
                
                return {
                    'id': info['id'],
                    'title': info['title'],
                    'duration': info['duration'],
                    'uploader': info['uploader'],
                    'upload_date': info['upload_date'],
                    'view_count': info.get('view_count', 0),
                    'like_count': info.get('like_count', 0),
                    'availability': info.get('availability', 'unknown'),
                    'formats': [f"{f['format_id']}: {f.get('format_note', 'N/A')}" 
                              for f in info.get('formats', [])]
                }
                
        except Exception as e:
            current_app.logger.error(f"Error getting video info: {e}")
            raise
