{"name": "unidynamics-frontend", "version": "1.0.0", "description": "UniDynamics Frontend - React Application", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.15.0", "axios": "^1.5.0", "video.js": "^8.5.2", "videojs-react": "^1.0.1", "@mui/material": "^5.14.5", "@mui/icons-material": "^5.14.3", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/x-data-grid": "^6.12.0", "react-dropzone": "^14.2.3", "react-player": "^2.13.0", "react-timeline-range-slider": "^2.0.1", "recharts": "^2.8.0", "date-fns": "^2.30.0", "lodash": "^4.17.21", "react-hot-toast": "^2.4.1", "react-hook-form": "^7.45.4", "js-cookie": "^3.0.5"}, "devDependencies": {"@types/js-cookie": "^3.0.3", "@types/lodash": "^4.14.197", "eslint": "^8.48.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.0.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,css,md}"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}