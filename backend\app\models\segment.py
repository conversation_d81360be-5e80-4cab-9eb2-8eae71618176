from datetime import datetime
from app import db


class Segment(db.Model):
    """Segment model for managing video segments/clips."""
    
    __tablename__ = 'segments'
    
    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('projects.id'), nullable=False)
    video_id = db.Column(db.Integer, db.<PERSON>ey('videos.id'), nullable=False)
    
    # Timing information
    start_time = db.Column(db.Float, nullable=False)  # Start time in seconds
    end_time = db.Column(db.Float, nullable=False)    # End time in seconds
    duration = db.Column(db.Float, nullable=False)    # Duration in seconds
    
    # Content information
    title = db.Column(db.String(200))
    description = db.Column(db.Text)
    transcript_text = db.Column(db.Text)  # Transcript text for this segment
    
    # AI analysis
    is_ai_suggested = db.Column(db.<PERSON>olean, default=False)
    ai_confidence = db.Column(db.Float)  # AI confidence score (0.0 to 1.0)
    ai_reasoning = db.Column(db.Text)    # Why AI suggested this segment
    topics = db.Column(db.JSON)          # List of topics/keywords
    importance_score = db.Column(db.Float)  # Importance score (0.0 to 1.0)
    
    # User selection
    is_selected = db.Column(db.Boolean, default=False)
    user_notes = db.Column(db.Text)
    
    # Timeline position (for multi-video projects)
    timeline_position = db.Column(db.Integer)  # Order in final video
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def __init__(self, project_id, video_id, start_time, end_time, **kwargs):
        self.project_id = project_id
        self.video_id = video_id
        self.start_time = start_time
        self.end_time = end_time
        self.duration = end_time - start_time
        
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def update_timing(self, start_time, end_time):
        """Update segment timing."""
        self.start_time = start_time
        self.end_time = end_time
        self.duration = end_time - start_time
        db.session.commit()
    
    def select(self, selected=True, timeline_position=None):
        """Select or deselect segment for inclusion in final video."""
        self.is_selected = selected
        if timeline_position is not None:
            self.timeline_position = timeline_position
        db.session.commit()
    
    def set_ai_analysis(self, confidence, reasoning, topics=None, importance_score=None):
        """Set AI analysis results."""
        self.is_ai_suggested = True
        self.ai_confidence = confidence
        self.ai_reasoning = reasoning
        self.topics = topics or []
        self.importance_score = importance_score
        db.session.commit()
    
    def get_formatted_time(self, time_value):
        """Format time in seconds to HH:MM:SS format."""
        hours = int(time_value // 3600)
        minutes = int((time_value % 3600) // 60)
        seconds = int(time_value % 60)
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"
    
    @property
    def formatted_start_time(self):
        """Get formatted start time."""
        return self.get_formatted_time(self.start_time)
    
    @property
    def formatted_end_time(self):
        """Get formatted end time."""
        return self.get_formatted_time(self.end_time)
    
    @property
    def formatted_duration(self):
        """Get formatted duration."""
        return self.get_formatted_time(self.duration)
    
    def overlaps_with(self, other_segment):
        """Check if this segment overlaps with another segment."""
        return (self.start_time < other_segment.end_time and 
                self.end_time > other_segment.start_time)
    
    def merge_with(self, other_segment):
        """Merge this segment with another segment (if they're adjacent/overlapping)."""
        if not self.overlaps_with(other_segment) and not self.is_adjacent_to(other_segment):
            return False
        
        # Update timing to encompass both segments
        new_start = min(self.start_time, other_segment.start_time)
        new_end = max(self.end_time, other_segment.end_time)
        
        self.update_timing(new_start, new_end)
        
        # Merge transcript text
        if other_segment.transcript_text:
            if self.transcript_text:
                self.transcript_text += " " + other_segment.transcript_text
            else:
                self.transcript_text = other_segment.transcript_text
        
        # Merge topics
        if other_segment.topics:
            if self.topics:
                self.topics = list(set(self.topics + other_segment.topics))
            else:
                self.topics = other_segment.topics
        
        db.session.commit()
        return True
    
    def is_adjacent_to(self, other_segment, tolerance=1.0):
        """Check if this segment is adjacent to another (within tolerance)."""
        return (abs(self.end_time - other_segment.start_time) <= tolerance or
                abs(other_segment.end_time - self.start_time) <= tolerance)
    
    def to_dict(self):
        """Convert segment to dictionary."""
        return {
            'id': self.id,
            'project_id': self.project_id,
            'video_id': self.video_id,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'duration': self.duration,
            'formatted_start_time': self.formatted_start_time,
            'formatted_end_time': self.formatted_end_time,
            'formatted_duration': self.formatted_duration,
            'title': self.title,
            'description': self.description,
            'transcript_text': self.transcript_text,
            'is_ai_suggested': self.is_ai_suggested,
            'ai_confidence': self.ai_confidence,
            'ai_reasoning': self.ai_reasoning,
            'topics': self.topics,
            'importance_score': self.importance_score,
            'is_selected': self.is_selected,
            'user_notes': self.user_notes,
            'timeline_position': self.timeline_position,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def __repr__(self):
        return f'<Segment {self.id} ({self.formatted_start_time}-{self.formatted_end_time})>'
