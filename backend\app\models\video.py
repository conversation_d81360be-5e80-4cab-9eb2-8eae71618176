from datetime import datetime
from enum import Enum
from app import db


class VideoStatus(Enum):
    """Video processing status enumeration."""
    PENDING = 'pending'
    DOWNLOADING = 'downloading'
    DOWNLOADED = 'downloaded'
    TRANSCRIBING = 'transcribing'
    TRANSCRIBED = 'transcribed'
    ANALYZING = 'analyzing'
    READY = 'ready'
    FAILED = 'failed'


class LicenseType(Enum):
    """Video license type enumeration."""
    CREATIVE_COMMONS = 'creative_commons'
    STANDARD_YOUTUBE = 'standard_youtube'
    UNKNOWN = 'unknown'


class Video(db.Model):
    """Video model for managing source videos."""
    
    __tablename__ = 'videos'
    
    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False)
    
    # YouTube information
    youtube_id = db.Column(db.String(20), nullable=False, index=True)
    youtube_url = db.Column(db.String(500), nullable=False)
    
    # Video metadata
    title = db.Column(db.String(500))
    description = db.Column(db.Text)
    channel_name = db.Column(db.String(200))
    channel_id = db.Column(db.String(50))
    duration = db.Column(db.Float)  # Duration in seconds
    view_count = db.Column(db.BigInteger)
    like_count = db.Column(db.BigInteger)
    
    # License and legal
    license_type = db.Column(db.Enum(LicenseType), default=LicenseType.UNKNOWN)
    is_republishable = db.Column(db.Boolean, default=False)
    
    # File information
    file_url = db.Column(db.String(500))  # Cloud storage URL
    file_size = db.Column(db.BigInteger)  # File size in bytes
    file_format = db.Column(db.String(10))  # e.g., 'mp4', 'webm'
    resolution = db.Column(db.String(20))  # e.g., '1920x1080'
    
    # Processing status
    status = db.Column(db.Enum(VideoStatus), default=VideoStatus.PENDING, nullable=False)
    error_message = db.Column(db.Text)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    downloaded_at = db.Column(db.DateTime)
    transcribed_at = db.Column(db.DateTime)
    
    # Relationships
    transcript = db.relationship('Transcript', backref='video', uselist=False, cascade='all, delete-orphan')
    segments = db.relationship('Segment', backref='video', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, project_id, youtube_url, **kwargs):
        self.project_id = project_id
        self.youtube_url = youtube_url
        self.youtube_id = self.extract_youtube_id(youtube_url)
        
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    @staticmethod
    def extract_youtube_id(url):
        """Extract YouTube video ID from URL."""
        import re
        
        patterns = [
            r'(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)',
            r'youtube\.com\/v\/([^&\n?#]+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        
        return None
    
    def update_status(self, status, error_message=None):
        """Update video processing status."""
        self.status = status
        self.error_message = error_message
        
        if status == VideoStatus.DOWNLOADED:
            self.downloaded_at = datetime.utcnow()
        elif status == VideoStatus.TRANSCRIBED:
            self.transcribed_at = datetime.utcnow()
        
        db.session.commit()
    
    def set_metadata(self, metadata):
        """Set video metadata from YouTube API response."""
        self.title = metadata.get('title')
        self.description = metadata.get('description')
        self.channel_name = metadata.get('channel_name')
        self.channel_id = metadata.get('channel_id')
        self.duration = metadata.get('duration')
        self.view_count = metadata.get('view_count')
        self.like_count = metadata.get('like_count')
        
        # Determine license type
        license_info = metadata.get('license', '').lower()
        if 'creative' in license_info and 'commons' in license_info:
            self.license_type = LicenseType.CREATIVE_COMMONS
            self.is_republishable = True
        else:
            self.license_type = LicenseType.STANDARD_YOUTUBE
            self.is_republishable = False
        
        db.session.commit()
    
    def set_file_info(self, file_url, file_size, file_format, resolution=None):
        """Set downloaded file information."""
        self.file_url = file_url
        self.file_size = file_size
        self.file_format = file_format
        self.resolution = resolution
        db.session.commit()
    
    def get_suggested_segments_count(self):
        """Get count of AI-suggested segments."""
        return self.segments.filter_by(is_ai_suggested=True).count()
    
    def get_selected_segments_count(self):
        """Get count of user-selected segments."""
        return self.segments.filter_by(is_selected=True).count()
    
    def to_dict(self, include_transcript=False, include_segments=False):
        """Convert video to dictionary."""
        data = {
            'id': self.id,
            'project_id': self.project_id,
            'youtube_id': self.youtube_id,
            'youtube_url': self.youtube_url,
            'title': self.title,
            'description': self.description,
            'channel_name': self.channel_name,
            'channel_id': self.channel_id,
            'duration': self.duration,
            'view_count': self.view_count,
            'like_count': self.like_count,
            'license_type': self.license_type.value if self.license_type else None,
            'is_republishable': self.is_republishable,
            'file_url': self.file_url,
            'file_size': self.file_size,
            'file_format': self.file_format,
            'resolution': self.resolution,
            'status': self.status.value,
            'error_message': self.error_message,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'downloaded_at': self.downloaded_at.isoformat() if self.downloaded_at else None,
            'transcribed_at': self.transcribed_at.isoformat() if self.transcribed_at else None,
            'suggested_segments_count': self.get_suggested_segments_count(),
            'selected_segments_count': self.get_selected_segments_count()
        }
        
        if include_transcript and self.transcript:
            data['transcript'] = self.transcript.to_dict()
        
        if include_segments:
            data['segments'] = [segment.to_dict() for segment in self.segments]
        
        return data
    
    def __repr__(self):
        return f'<Video {self.youtube_id}>'
